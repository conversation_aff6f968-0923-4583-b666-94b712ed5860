import { supabase } from "./supabase";

// Types for search results
export interface UserSearchResult {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  role: string;
  institution: string | null;
}

export interface DocumentSearchResult {
  id: string;
  title: string;
  description: string | null;
  file_type: string;
  file_size: number;
  is_public: boolean;
  created_at: string;
  user_id: string;
  owner_name: string | null;
}

export interface RoomSearchResult {
  id: string;
  name: string;
  description: string | null;
  room_code: string;
  is_private: boolean;
  member_count: number;
  created_at: string;
  created_by: string;
  creator_name: string | null;
}

export interface GlobalSearchResults {
  users: UserSearchResult[];
  documents: DocumentSearchResult[];
  rooms: RoomSearchResult[];
}

export const globalSearchService = {
  // Search across all content types
  async searchAll(
    query: string,
    currentUserId: string
  ): Promise<GlobalSearchResults> {
    if (!query || query.length < 2) {
      return { users: [], documents: [], rooms: [] };
    }

    try {
      const [users, documents, rooms] = await Promise.all([
        this.searchUsers(query, currentUserId),
        this.searchDocuments(query, currentUserId),
        this.searchRooms(query, currentUserId),
      ]);

      return { users, documents, rooms };
    } catch (error: any) {
      console.error("Global search error:", error);
      throw new Error("Search failed. Please try again.");
    }
  },

  // Search users
  async searchUsers(
    query: string,
    currentUserId: string
  ): Promise<UserSearchResult[]> {
    try {
      const searchTerm = query.trim();

      const { data: users, error } = await supabase
        .from("profiles")
        .select("id, email, full_name, avatar_url, role, institution")
        .neq("id", currentUserId)
        .or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
        .limit(10);

      if (error) {
        console.error("User search error:", error);
        return [];
      }

      return users || [];
    } catch (error: any) {
      console.error("User search error:", error);
      return [];
    }
  },

  // Search public documents
  async searchDocuments(
    query: string,
    currentUserId: string
  ): Promise<DocumentSearchResult[]> {
    try {
      const searchTerm = query.trim();

      // First, search for public documents
      const { data: publicDocs, error: publicError } = await supabase
        .from("documents")
        .select(
          `
          id,
          title,
          description,
          file_type,
          file_size,
          is_public,
          created_at,
          user_id,
          profiles!documents_user_id_fkey(full_name)
        `
        )
        .eq("is_public", true)
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
        .limit(5);

      // Then, search for documents in public rooms
      const { data: roomDocs, error: roomError } = await supabase
        .from("room_documents")
        .select(
          `
          documents!inner(
            id,
            title,
            description,
            file_type,
            file_size,
            is_public,
            created_at,
            user_id,
            profiles!documents_user_id_fkey(full_name)
          ),
          rooms!inner(is_private)
        `
        )
        .eq("rooms.is_private", false)
        .or(
          `documents.title.ilike.%${searchTerm}%,documents.description.ilike.%${searchTerm}%`
        )
        .limit(5);

      const allDocuments: any[] = [];

      if (!publicError && publicDocs) {
        allDocuments.push(...publicDocs);
      }

      if (!roomError && roomDocs) {
        // Extract documents from room_documents join
        const roomDocuments = roomDocs.map((rd) => (rd as any).documents);
        allDocuments.push(...roomDocuments);
      }

      // Remove duplicates and limit results
      const uniqueDocuments = allDocuments
        .filter(
          (doc, index, self) => index === self.findIndex((d) => d.id === doc.id)
        )
        .slice(0, 10);

      return uniqueDocuments.map((doc) => ({
        id: doc.id,
        title: doc.title,
        description: doc.description,
        file_type: doc.file_type,
        file_size: doc.file_size,
        is_public: doc.is_public,
        created_at: doc.created_at,
        user_id: doc.user_id,
        owner_name: (doc.profiles as any)?.full_name || null,
      }));
    } catch (error: any) {
      console.error("Document search error:", error);
      return [];
    }
  },

  // Search public rooms
  async searchRooms(
    query: string,
    currentUserId: string
  ): Promise<RoomSearchResult[]> {
    try {
      const searchTerm = query.trim();

      const { data: rooms, error } = await supabase
        .from("rooms")
        .select(
          `
          id,
          name,
          description,
          room_code,
          is_private,
          created_at,
          created_by,
          profiles!rooms_created_by_fkey(full_name)
        `
        )
        .eq("is_private", false)
        .or(
          `name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,room_code.ilike.%${searchTerm}%`
        )
        .limit(10);

      if (error) {
        console.error("Room search error:", error);
        return [];
      }

      // Get member counts separately to avoid RLS issues
      const roomsWithCounts = await Promise.all(
        (rooms || []).map(async (room) => {
          try {
            const { count } = await supabase
              .from("room_members")
              .select("*", { count: "exact", head: true })
              .eq("room_id", room.id);

            return {
              id: room.id,
              name: room.name,
              description: room.description,
              room_code: room.room_code,
              is_private: room.is_private,
              member_count: count || 0,
              created_at: room.created_at,
              created_by: room.created_by,
              creator_name: (room.profiles as any)?.full_name || null,
            };
          } catch (error) {
            // If we can't get the count, default to 0
            return {
              id: room.id,
              name: room.name,
              description: room.description,
              room_code: room.room_code,
              is_private: room.is_private,
              member_count: 0,
              created_at: room.created_at,
              created_by: room.created_by,
              creator_name: (room.profiles as any)?.full_name || null,
            };
          }
        })
      );

      return roomsWithCounts;
    } catch (error: any) {
      console.error("Room search error:", error);
      return [];
    }
  },
};
