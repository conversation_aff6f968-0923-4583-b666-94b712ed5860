import { supabase } from "./supabase";
import { formatFileSize } from "./utils";
import { notificationService } from "./notificationService";
import { storageService } from "./storageService";

export interface DocumentFile {
  id: string;
  user_id: string;
  folder_id: string | null;
  title: string;
  description: string | null;
  file_path: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  is_public: boolean;
  download_count: number;
  created_at: string;
  updated_at: string;
  // Shared document properties
  is_shared_copy?: boolean;
  original_document_id?: string;
  shared_from_user_id?: string;
  shared_at?: string;
}

export interface Room {
  id: string;
  name: string;
  description: string | null;
  room_code: string;
  created_by: string;
  is_private: boolean;
  max_members: number;
  created_at: string;
  updated_at: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
}

// Allowed file types and their extensions
export const ALLOWED_FILE_TYPES = {
  "application/pdf": [".pdf"],
  "application/msword": [".doc"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
    ".docx",
  ],
  "application/vnd.ms-powerpoint": [".ppt"],
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": [
    ".pptx",
  ],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx",
  ],
  "text/plain": [".txt"],
  "image/jpeg": [".jpg", ".jpeg"],
  "image/png": [".png"],
  "image/gif": [".gif"],
  "image/webp": [".webp"],
  "image/svg+xml": [".svg"],
};

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export const fileService = {
  // Validate file before upload
  validateFile(file: File): FileValidationResult {
    const errors: string[] = [];

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      errors.push(
        `File size must be less than ${formatFileSize(MAX_FILE_SIZE)}`
      );
    }

    // Check file type
    const allowedTypes = Object.keys(ALLOWED_FILE_TYPES);
    if (!allowedTypes.includes(file.type)) {
      const allowedExtensions = Object.values(ALLOWED_FILE_TYPES).flat();
      errors.push(
        `File type not allowed. Supported types: ${allowedExtensions.join(
          ", "
        )}`
      );
    }

    // Check file name
    if (!file.name || file.name.trim().length === 0) {
      errors.push("File must have a valid name");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Generate unique file path
  generateFilePath(userId: string, fileName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = fileName.substring(fileName.lastIndexOf("."));
    const baseName = fileName
      .substring(0, fileName.lastIndexOf("."))
      .replace(/[^a-zA-Z0-9]/g, "_");
    return `${userId}/documents/${timestamp}_${randomString}_${baseName}${extension}`;
  },

  // Upload file to Supabase Storage
  async uploadFile(file: File, userId: string): Promise<string> {
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(", "));
    }

    const filePath = this.generateFilePath(userId, file.name);

    try {
      const { data, error } = await supabase.storage
        .from("documents")
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) throw error;

      return data.path;
    } catch (error: any) {
      console.error("File upload error:", error);
      throw new Error("Unable to upload file. Please try again later.");
    }
  },

  // Create document record in database
  async createDocument(data: {
    title: string;
    description?: string;
    file_path: string;
    file_size: number;
    file_type: string;
    mime_type: string;
    is_public?: boolean;
    user_id: string;
    folder_id?: string | null;
  }): Promise<DocumentFile> {
    try {
      const { data: document, error } = await supabase
        .from("documents")
        .insert({
          title: data.title,
          description: data.description || null,
          file_path: data.file_path,
          file_size: data.file_size,
          file_type: data.file_type,
          mime_type: data.mime_type,
          is_public: data.is_public || false,
          user_id: data.user_id,
          folder_id: data.folder_id || null,
        })
        .select()
        .single();

      if (error) throw error;
      return document;
    } catch (error: any) {
      console.error("Create document error:", error);
      throw new Error(
        "Unable to create document record. Please try again later."
      );
    }
  },

  // Upload file and create document record
  async uploadDocument(
    file: File,
    title: string,
    description: string,
    userId: string,
    isPublic: boolean = false,
    folderId?: string | null
  ): Promise<DocumentFile> {
    try {
      // Upload file to storage
      const filePath = await this.uploadFile(file, userId);

      // Create document record
      const document = await this.createDocument({
        title,
        description,
        file_path: filePath,
        file_size: file.size,
        file_type: this.getFileType(file.name),
        mime_type: file.type,
        is_public: isPublic,
        user_id: userId,
        folder_id: folderId,
      });

      return document;
    } catch (error: any) {
      console.error("Upload document error:", error);
      throw error;
    }
  },

  // Get user's documents (including permanently shared documents)
  async getUserDocuments(userId: string): Promise<DocumentFile[]> {
    try {
      // Get all documents owned by user (including permanently shared copies)
      const { data: documents, error } = await supabase
        .from("documents")
        .select("*")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return documents || [];
    } catch (error: any) {
      console.error("Get user documents error:", error);
      throw new Error(error.message || "Failed to fetch documents");
    }
  },

  // Get documents in a specific folder
  async getDocumentsByFolder(
    userId: string,
    folderId: string | null
  ): Promise<DocumentFile[]> {
    try {
      let query = supabase.from("documents").select("*").eq("user_id", userId);

      // Handle null folderId properly for root documents
      if (folderId === null) {
        query = query.is("folder_id", null);
      } else {
        query = query.eq("folder_id", folderId);
      }

      const { data, error } = await query.order("created_at", {
        ascending: false,
      });

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      console.error("Get documents by folder error:", error);
      throw new Error(error.message || "Failed to fetch documents");
    }
  },

  // Get document by ID
  async getDocumentById(documentId: string): Promise<DocumentFile> {
    try {
      const { data: document, error } = await supabase
        .from("documents")
        .select("*")
        .eq("id", documentId)
        .single();

      if (error) throw error;
      if (!document) throw new Error("Document not found");

      return document;
    } catch (error: any) {
      console.error("Get document by ID error:", error);
      throw new Error(error.message || "Failed to fetch document");
    }
  },

  // Get shared documents (documents shared with user)
  async getSharedDocuments(userId: string): Promise<DocumentFile[]> {
    try {
      // Get documents that are shared copies owned by the user
      const { data: documents, error } = await supabase
        .from("documents")
        .select("*")
        .eq("user_id", userId)
        .eq("is_shared_copy", true)
        .order("shared_at", { ascending: false });

      if (error) throw error;
      return documents || [];
    } catch (error: any) {
      console.error("Get shared documents error:", error);
      // Return empty array instead of throwing to prevent app crash
      return [];
    }
  },

  // Delete document
  async deleteDocument(documentId: string, userId: string): Promise<void> {
    try {
      // First get the document to check ownership and get file path
      const { data: document, error: fetchError } = await supabase
        .from("documents")
        .select("file_path, user_id, title")
        .eq("id", documentId)
        .single();

      if (fetchError) {
        console.error("Error fetching document:", fetchError);
        throw fetchError;
      }
      if (!document) throw new Error("Document not found");
      if (document.user_id !== userId)
        throw new Error("Not authorized to delete this document");

      // Delete from storage first to ensure file is removed
      try {
        const { error: storageError } = await supabase.storage
          .from("documents")
          .remove([document.file_path]);

        if (storageError) {
          console.error("Storage deletion error:", storageError);
          // Continue with database deletion even if storage fails
        }
      } catch (storageError) {
        console.error("Storage deletion exception:", storageError);
        // Continue with database deletion
      }

      // Delete from database with explicit user_id check for RLS
      const { error: deleteError, count } = await supabase
        .from("documents")
        .delete({ count: "exact" })
        .eq("id", documentId)
        .eq("user_id", userId); // Explicit user_id check for RLS

      if (deleteError) {
        console.error("Database deletion error:", deleteError);
        throw deleteError;
      }

      if (count === 0) {
        throw new Error("Document not found or not authorized to delete");
      }

      // Verify deletion by trying to fetch the document
      const { data: verifyDoc } = await supabase
        .from("documents")
        .select("id")
        .eq("id", documentId)
        .single();

      if (verifyDoc) {
        console.error("Document still exists after deletion attempt!");
        throw new Error(
          "Document deletion failed - record still exists in database"
        );
      }
    } catch (error: any) {
      console.error("Delete document error:", error);
      throw new Error(error.message || "Failed to delete document");
    }
  },

  // Bulk delete documents
  async bulkDeleteDocuments(
    documentIds: string[],
    userId: string
  ): Promise<{ success: string[]; failed: string[] }> {
    const success: string[] = [];
    const failed: string[] = [];

    for (const documentId of documentIds) {
      try {
        await this.deleteDocument(documentId, userId);
        success.push(documentId);
      } catch (error) {
        console.error(`Failed to delete document ${documentId}:`, error);
        failed.push(documentId);
      }
    }

    return { success, failed };
  },

  // Move documents to folder
  async moveDocumentsToFolder(
    documentIds: string[],
    folderId: string | null,
    userId: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from("documents")
        .update({ folder_id: folderId })
        .in("id", documentIds)
        .eq("user_id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Move documents to folder error:", error);
      throw new Error(error.message || "Failed to move documents");
    }
  },

  // Share document with individual user (permanent sharing)
  async shareDocumentWithUser(
    documentId: string,
    sharedWith: string,
    sharedBy: string,
    permission: "view" | "download" = "download",
    _expiresAt?: string
  ): Promise<void> {
    try {
      // Check if document exists and user owns it
      const { data: document, error: docError } = await supabase
        .from("documents")
        .select("*")
        .eq("id", documentId)
        .eq("user_id", sharedBy)
        .single();

      if (docError || !document) {
        throw new Error(
          "Document not found or you don't have permission to share it"
        );
      }

      // Check if already shared with this user
      const { data: existingShare } = await supabase
        .from("document_shares")
        .select("id")
        .eq("shared_with", sharedWith)
        .eq("original_document_id", documentId)
        .eq("is_permanent", true)
        .maybeSingle();

      if (existingShare) {
        throw new Error("Document is already shared with this user");
      }

      // Check if recipient has enough storage space
      const hasSpace = await storageService.checkStorageAvailable(
        sharedWith,
        document.file_size
      );
      if (!hasSpace) {
        throw new Error(
          "Recipient doesn't have enough storage space for this document"
        );
      }

      // Create permanent document copy immediately
      const newDocumentId = await this.createPermanentDocumentCopy(
        document,
        sharedWith,
        sharedBy,
        permission
      );

      // Get user details for notification
      const { data: sharerProfile } = await supabase
        .from("profiles")
        .select("full_name, email")
        .eq("id", sharedBy)
        .single();

      // Send notification to recipient
      try {
        await notificationService.notifyDocumentSharedDirect({
          recipientId: sharedWith,
          sharerName:
            sharerProfile?.full_name || sharerProfile?.email || "Someone",
          documentTitle: document.title,
          documentId: newDocumentId, // Use the new document ID
          shareId: newDocumentId, // Use document ID as reference
        });
      } catch (notificationError) {
        console.error("Failed to send share notification:", notificationError);
        // Don't throw error here as the share was successful
      }
    } catch (error: any) {
      console.error("Share document with user error:", error);
      throw new Error(error.message || "Failed to share document with user");
    }
  },

  // Create shared document via notification system (RLS-compatible)
  async createSharedDocumentViaNotification(
    originalDocument: any,
    recipientUserId: string,
    sharedBy: string,
    permission: string
  ): Promise<string> {
    try {
      // For now, create a reference-based share that will be converted to permanent
      // when the recipient first accesses it
      const { data: share, error: shareError } = await supabase
        .from("document_shares")
        .insert({
          original_document_id: originalDocument.id,
          shared_document_id: null, // Will be populated when converted
          shared_by: sharedBy,
          shared_with: recipientUserId,
          permission,
          is_permanent: false, // Will be set to true when converted
          shared_at: new Date().toISOString(),
          original_owner_id: sharedBy,
        })
        .select()
        .single();

      if (shareError) {
        throw new Error(`Failed to create share record: ${shareError.message}`);
      }

      return share.id;
    } catch (error: any) {
      console.error("Create shared document via notification error:", error);
      throw new Error(error.message || "Failed to create shared document");
    }
  },

  // Convert reference share to permanent copy (called when user first accesses)
  async convertToPermanentShare(
    shareId: string,
    userId: string
  ): Promise<string> {
    try {
      // Get the share record
      const { data: share, error: shareError } = await supabase
        .from("document_shares")
        .select("*, documents!original_document_id(*)")
        .eq("id", shareId)
        .eq("shared_with", userId)
        .single();

      if (shareError || !share) {
        throw new Error("Share not found");
      }

      if (share.is_permanent) {
        return share.shared_document_id; // Already converted
      }

      const originalDoc = share.documents;

      // Generate new file path
      const timestamp = Date.now();
      const extension = originalDoc.file_path.split(".").pop() || "";
      const cleanTitle = originalDoc.title.replace(/[^a-zA-Z0-9]/g, "_");
      const newFileName = `${timestamp}_shared_${cleanTitle}.${extension}`;
      const newFilePath = `${userId}/documents/${newFileName}`;

      // Create document record (user creates for themselves)
      const { data: newDoc, error: createError } = await supabase
        .from("documents")
        .insert({
          user_id: userId,
          folder_id: null,
          title: `${originalDoc.title} (shared)`,
          description: originalDoc.description,
          file_path: newFilePath,
          file_size: originalDoc.file_size,
          file_type: originalDoc.file_type,
          mime_type: originalDoc.mime_type,
          is_public: false,
          is_shared_copy: true,
          original_document_id: originalDoc.id,
          shared_from_user_id: share.shared_by,
          shared_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Failed to create document: ${createError.message}`);
      }

      // Copy the file
      await this.copyFileWithRLS(originalDoc.file_path, newFilePath);

      // Update share record
      const { error: updateError } = await supabase
        .from("document_shares")
        .update({
          shared_document_id: newDoc.id,
          is_permanent: true,
        })
        .eq("id", shareId);

      if (updateError) {
        throw new Error(`Failed to update share: ${updateError.message}`);
      }

      return newDoc.id;
    } catch (error: any) {
      console.error("Convert to permanent share error:", error);
      throw new Error(error.message || "Failed to convert to permanent share");
    }
  },

  // Create permanent document copy with proper RLS handling
  async createPermanentDocumentCopy(
    originalDocument: any,
    recipientUserId: string,
    sharedBy: string,
    permission: string
  ): Promise<string> {
    try {
      // Generate new file path for recipient
      const timestamp = Date.now();
      const extension = originalDocument.file_path.split(".").pop() || "";
      const cleanTitle = originalDocument.title.replace(/[^a-zA-Z0-9]/g, "_");
      const newFileName = `${timestamp}_shared_${cleanTitle}.${extension}`;
      const newFilePath = `${recipientUserId}/documents/${newFileName}`;

      // Step 1: Create document record using admin client to bypass RLS
      const { supabaseAdmin } = await import("./supabase");
      if (!supabaseAdmin) {
        throw new Error("Admin client not available for document sharing");
      }

      const { data: newDoc, error: createError } = await supabaseAdmin
        .from("documents")
        .insert({
          user_id: recipientUserId,
          folder_id: null,
          title: `${originalDocument.title} (shared)`,
          description: originalDocument.description,
          file_path: newFilePath, // Set the target path
          file_size: originalDocument.file_size,
          file_type: originalDocument.file_type,
          mime_type: originalDocument.mime_type,
          is_public: false,
          is_shared_copy: true,
          original_document_id: originalDocument.id,
          shared_from_user_id: sharedBy,
          shared_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (createError) {
        throw new Error(
          `Failed to create document record: ${createError.message}`
        );
      }

      // Step 2: Copy file using a simpler approach that works with RLS
      await this.copyFileWithRLS(originalDocument.file_path, newFilePath);

      // Step 3: Create share tracking record
      const { error: shareError } = await supabase
        .from("document_shares")
        .insert({
          original_document_id: originalDocument.id,
          shared_document_id: newDoc.id,
          shared_by: sharedBy,
          shared_with: recipientUserId,
          permission,
        });

      if (shareError) {
        // Clean up document record if share creation fails
        await supabase.from("documents").delete().eq("id", newDoc.id);
        throw new Error(`Failed to create share record: ${shareError.message}`);
      }

      return newDoc.id;
    } catch (error: any) {
      console.error("Create permanent document copy error:", error);
      throw new Error(
        error.message || "Failed to create permanent document copy"
      );
    }
  },

  // Copy file with RLS-compatible approach
  async copyFileWithRLS(sourcePath: string, targetPath: string): Promise<void> {
    try {
      // Download the file first
      const { data: fileData, error: downloadError } = await supabase.storage
        .from("documents")
        .download(sourcePath);

      if (downloadError) {
        throw new Error(
          `Failed to download source file: ${downloadError.message}`
        );
      }

      // Upload to target location
      const { error: uploadError } = await supabase.storage
        .from("documents")
        .upload(targetPath, fileData, {
          cacheControl: "3600",
          upsert: false,
        });

      if (uploadError) {
        throw new Error(`Failed to upload file copy: ${uploadError.message}`);
      }
    } catch (error: any) {
      console.error("Copy file with RLS error:", error);
      throw new Error(error.message || "Failed to copy file");
    }
  },

  // Copy file in storage for permanent sharing
  async copyFileInStorage(
    originalDocument: any,
    recipientUserId: string,
    newDocumentId: string
  ): Promise<void> {
    try {
      // Generate new file path
      const timestamp = Date.now();
      const extension = originalDocument.file_path.split(".").pop() || "";
      const newFileName = `${timestamp}_shared_${originalDocument.title.replace(
        /[^a-zA-Z0-9]/g,
        "_"
      )}.${extension}`;
      const newFilePath = `${recipientUserId}/documents/${newFileName}`;

      // Download original file
      const { data: fileData, error: downloadError } = await supabase.storage
        .from("documents")
        .download(originalDocument.file_path);

      if (downloadError) {
        throw new Error(
          `Failed to download original file: ${downloadError.message}`
        );
      }

      // Upload to recipient's storage
      const { error: uploadError } = await supabase.storage
        .from("documents")
        .upload(newFilePath, fileData, {
          cacheControl: "3600",
          upsert: false,
        });

      if (uploadError) {
        throw new Error(`Failed to upload file copy: ${uploadError.message}`);
      }

      // Update document record with correct file path
      const { error: updateError } = await supabase
        .from("documents")
        .update({ file_path: newFilePath })
        .eq("id", newDocumentId);

      if (updateError) {
        throw new Error(
          `Failed to update document path: ${updateError.message}`
        );
      }
    } catch (error: any) {
      console.error("Copy file in storage error:", error);
      throw new Error(error.message || "Failed to copy file in storage");
    }
  },

  // Get document shares for a specific document
  async getDocumentShares(
    documentId: string,
    userId: string
  ): Promise<
    Array<{
      id: string;
      shared_with: string;
      shared_by: string;
      permission: "view" | "download";
      expires_at: string | null;
      created_at: string;
      user_profile?: {
        full_name: string | null;
        email: string;
        role: string;
      };
    }>
  > {
    try {
      // Check if user has access to the document (owns it or it's shared with them)
      const { data: document, error: docError } = await supabase
        .from("documents")
        .select("user_id")
        .eq("id", documentId)
        .single();

      if (docError || !document) {
        throw new Error("Document not found");
      }

      // Check if user owns the document OR if it's shared with them
      const isOwner = document.user_id === userId;
      const { data: shareCheck } = await supabase
        .from("document_shares")
        .select("id")
        .eq("shared_with", userId)
        .or(
          `original_document_id.eq.${documentId},shared_document_id.eq.${documentId}`
        )
        .maybeSingle();

      if (!isOwner && !shareCheck) {
        throw new Error("Access denied");
      }

      // Get shares first (with backward compatibility)
      const { data: shares, error: sharesError } = await supabase
        .from("document_shares")
        .select(
          "id, shared_with, shared_by, permission, expires_at, created_at"
        )
        .or(
          `original_document_id.eq.${documentId},document_id.eq.${documentId}`
        )
        .order("created_at", { ascending: false });

      if (sharesError) throw sharesError;

      if (!shares || shares.length === 0) {
        return [];
      }

      // Get profiles for shared_with users
      const sharedWithIds = shares.map((share) => share.shared_with);
      const { data: profiles, error: profilesError } = await supabase
        .from("profiles")
        .select("id, full_name, email, role")
        .in("id", sharedWithIds);

      if (profilesError) {
        console.error(
          "Error fetching profiles for document shares:",
          profilesError
        );
      }

      return shares.map((share: any) => {
        const profile = profiles?.find((prof) => prof.id === share.shared_with);
        return {
          ...share,
          shared_with_profile: profile
            ? {
                full_name: profile.full_name,
                email: profile.email,
                role: profile.role,
              }
            : undefined,
        };
      });
    } catch (error: any) {
      console.error("Get document shares error:", error);
      throw new Error(error.message || "Failed to get document shares");
    }
  },

  // Revoke document share from a user (permanent sharing)
  async revokeDocumentShare(shareId: string, userId: string): Promise<void> {
    try {
      // Get the share record with both document IDs
      const { data: share, error: shareError } = await supabase
        .from("document_shares")
        .select(
          "id, shared_by, shared_with, original_document_id, shared_document_id"
        )
        .eq("id", shareId)
        .single();

      if (shareError || !share) {
        throw new Error("Share not found");
      }

      // Verify the original document owner
      const { data: originalDoc, error: docError } = await supabase
        .from("documents")
        .select("user_id")
        .eq("id", share.original_document_id)
        .single();

      if (docError || !originalDoc) {
        throw new Error("Original document not found");
      }

      // Check if user can revoke this share (only the original owner can revoke)
      if (originalDoc.user_id !== userId) {
        throw new Error("You don't have permission to revoke this share");
      }

      // Delete the shared document copy first
      if (share.shared_document_id) {
        await this.deleteDocument(share.shared_document_id, share.shared_with);
      }

      // Delete the share record
      const { error: deleteError } = await supabase
        .from("document_shares")
        .delete()
        .eq("id", shareId);

      if (deleteError) throw deleteError;
    } catch (error: any) {
      console.error("Revoke document share error:", error);
      throw new Error(error.message || "Failed to revoke document share");
    }
  },

  // Update document share permission
  async updateDocumentSharePermission(
    shareId: string,
    permission: "view" | "download",
    userId: string
  ): Promise<void> {
    try {
      // Verify user created the share
      const { data: share, error: shareError } = await supabase
        .from("document_shares")
        .select("shared_by")
        .eq("id", shareId)
        .eq("shared_by", userId)
        .single();

      if (shareError || !share) {
        throw new Error(
          "Share not found or you don't have permission to modify it"
        );
      }

      // Update the permission
      const { error: updateError } = await supabase
        .from("document_shares")
        .update({ permission })
        .eq("id", shareId);

      if (updateError) throw updateError;
    } catch (error: any) {
      console.error("Update document share permission error:", error);
      throw new Error(error.message || "Failed to update share permission");
    }
  },

  // Get documents shared with current user (enhanced version)
  async getSharedDocumentsWithDetails(userId: string): Promise<
    Array<
      DocumentFile & {
        share_info: {
          shared_by: string;
          permission: "view" | "download";
          created_at: string;
          expires_at: string | null;
          sharer_profile?: {
            full_name: string | null;
            email: string;
            role: string;
          };
        };
      }
    >
  > {
    try {
      // First, let's get the document shares
      const { data: shares, error: sharesError } = await supabase
        .from("document_shares")
        .select("*")
        .eq("shared_with", userId)
        .order("created_at", { ascending: false });

      if (sharesError) {
        console.error("Error fetching document shares:", sharesError);
        throw sharesError;
      }

      if (!shares || shares.length === 0) {
        return [];
      }

      // Get the documents and profiles separately to avoid foreign key issues
      // For reference-based sharing, we need to get the original documents
      const documentIds = shares
        .map((share) => {
          // If shared_document_id exists, use it (permanent sharing)
          // Otherwise use original_document_id (reference-based sharing)
          // Fall back to document_id for old schema
          return (
            share.shared_document_id ||
            share.original_document_id ||
            share.document_id
          );
        })
        .filter((id) => id != null);

      const sharedByIds = shares
        .map((share) => share.shared_by)
        .filter((id) => id != null);

      if (documentIds.length === 0) {
        return [];
      }

      // Get documents
      const { data: documents, error: documentsError } = await supabase
        .from("documents")
        .select("*")
        .in("id", documentIds);

      if (documentsError) {
        console.error("Error fetching documents:", documentsError);
        throw documentsError;
      }

      // Get profiles
      const { data: profiles, error: profilesError } = await supabase
        .from("profiles")
        .select("id, full_name, email, role")
        .in("id", sharedByIds);

      if (profilesError) {
        console.error("Error fetching profiles:", profilesError);
        // Don't throw here, just log the error
      }

      // Combine the data
      const result = shares
        .map((share: any) => {
          const documentId =
            share.shared_document_id ||
            share.original_document_id ||
            share.document_id;
          const document = documents?.find((doc) => doc.id === documentId);
          const profile = profiles?.find((prof) => prof.id === share.shared_by);

          if (!document) {
            return null;
          }

          return {
            ...document,
            share_info: {
              shared_by: share.shared_by,
              permission: share.permission,
              created_at: share.created_at,
              expires_at: share.expires_at,
              sharer_profile: profile
                ? {
                    full_name: profile.full_name,
                    email: profile.email,
                    role: profile.role,
                  }
                : undefined,
            },
          };
        })
        .filter(Boolean); // Remove null entries

      return result;
    } catch (error: any) {
      console.error("Get shared documents with details error:", error);
      return [];
    }
  },

  // Get download URL for a document
  async getDownloadUrl(filePath: string): Promise<string> {
    try {
      const { data, error } = await supabase.storage
        .from("documents")
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (error) throw error;
      return data.signedUrl;
    } catch (error: any) {
      console.error("Get download URL error:", error);
      throw new Error(error.message || "Failed to get download URL");
    }
  },

  // Get download URL for a document with proper access handling
  async getDocumentDownloadUrl(
    documentId: string,
    userId: string
  ): Promise<string> {
    try {
      // Get the document to check if it's shared
      const { data: document, error: docError } = await supabase
        .from("documents")
        .select("*")
        .eq("id", documentId)
        .single();

      if (docError || !document) {
        throw new Error("Document not found");
      }

      // Check if user owns the document
      if (document.user_id === userId) {
        return await this.getDownloadUrl(document.file_path);
      }

      // Check if document is shared with user
      const { data: share } = await supabase
        .from("document_shares")
        .select("*")
        .eq("shared_with", userId)
        .or(
          `original_document_id.eq.${documentId},shared_document_id.eq.${documentId}`
        )
        .single();

      if (share) {
        return await this.getDownloadUrl(document.file_path);
      }

      throw new Error("Access denied");
    } catch (error: any) {
      console.error("Get document download URL error:", error);
      throw new Error(error.message || "Failed to get document download URL");
    }
  },

  // Download document with proper filename and progress tracking
  async downloadDocument(
    document: DocumentFile,
    onProgress?: (progress: number) => void,
    userId?: string
  ): Promise<void> {
    try {
      const downloadUrl = userId
        ? await this.getDocumentDownloadUrl(document.id, userId)
        : await this.getDownloadUrl(document.file_path);

      // Create a proper filename from the document title and file extension
      const extension = document.file_path.split(".").pop() || "";
      const filename = `${document.title}.${extension}`;

      // Use fetch to download with progress tracking
      const response = await fetch(downloadUrl);
      if (!response.ok) throw new Error("Download failed");

      const contentLength = response.headers.get("content-length");
      const total = contentLength ? parseInt(contentLength, 10) : 0;

      if (!response.body) throw new Error("No response body");

      const reader = response.body.getReader();
      const chunks: Uint8Array[] = [];
      let loaded = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        loaded += value.length;

        if (onProgress && total > 0) {
          onProgress((loaded / total) * 100);
        }
      }

      // Create blob and download
      const blob = new Blob(chunks);
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement("a");
      link.href = url;
      link.download = filename;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      // Update download count
      await this.incrementDownloadCount(document.id);
    } catch (error: any) {
      console.error("Download document error:", error);
      throw new Error(error.message || "Failed to download document");
    }
  },

  // Increment download count
  async incrementDownloadCount(documentId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc("increment_download_count", {
        document_id: documentId,
      });

      if (error) {
        // Fallback to manual increment if RPC doesn't exist
        const { data: doc } = await supabase
          .from("documents")
          .select("download_count")
          .eq("id", documentId)
          .single();

        if (doc) {
          await supabase
            .from("documents")
            .update({ download_count: (doc.download_count || 0) + 1 })
            .eq("id", documentId);
        }
      }
    } catch (error: any) {
      // Don't throw error as download was successful
    }
  },

  // Update document
  async updateDocument(
    documentId: string,
    updates: {
      title?: string;
      description?: string;
      is_public?: boolean;
    },
    userId: string
  ): Promise<DocumentFile> {
    try {
      const { data, error } = await supabase
        .from("documents")
        .update(updates)
        .eq("id", documentId)
        .eq("user_id", userId)
        .select()
        .single();

      if (error) throw error;
      if (!data) throw new Error("Document not found or not authorized");
      return data;
    } catch (error: any) {
      console.error("Update document error:", error);
      throw new Error(error.message || "Failed to update document");
    }
  },

  // Get file type from filename
  getFileType(fileName: string): string {
    const extension = fileName
      .substring(fileName.lastIndexOf("."))
      .toLowerCase();

    if ([".pdf"].includes(extension)) return "PDF";
    if ([".doc", ".docx"].includes(extension)) return "Word Document";
    if ([".ppt", ".pptx"].includes(extension)) return "Presentation";
    if ([".xls", ".xlsx"].includes(extension)) return "Spreadsheet";
    if ([".txt"].includes(extension)) return "Text File";
    if ([".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"].includes(extension))
      return "Image";

    return "Document";
  },

  // Get file icon emoji
  getFileIcon(fileType: string): string {
    switch (fileType.toLowerCase()) {
      case "pdf":
        return "📄";
      case "word document":
        return "📝";
      case "presentation":
        return "📽️";
      case "spreadsheet":
        return "📊";
      case "text file":
        return "📄";
      case "image":
        return "🖼️";
      default:
        return "📁";
    }
  },

  // Check if file type supports preview
  canPreview(fileType: string, mimeType: string): boolean {
    const previewableTypes = [
      "pdf",
      "image",
      "text file",
      "word document",
      "presentation",
      "spreadsheet",
    ];
    const previewableMimeTypes = [
      "application/pdf",
      "text/plain",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel",
    ];

    return (
      previewableTypes.includes(fileType.toLowerCase()) ||
      mimeType.startsWith("image/") ||
      previewableMimeTypes.includes(mimeType)
    );
  },

  // Get preview URL for supported file types
  async getPreviewUrl(
    document: DocumentFile,
    userId?: string
  ): Promise<string> {
    try {
      // If userId is provided, use access-controlled method
      if (userId) {
        return await this.getDocumentDownloadUrl(document.id, userId);
      }

      // Fallback to direct file path access (for owned documents)
      return await this.getDownloadUrl(document.file_path);
    } catch (error: any) {
      console.error("Get preview URL error:", error);
      throw new Error(error.message || "Failed to get preview URL");
    }
  },

  // Get file content for text files
  async getTextContent(document: DocumentFile): Promise<string> {
    try {
      if (document.mime_type !== "text/plain") {
        throw new Error("Not a text file");
      }

      const url = await this.getDownloadUrl(document.file_path);
      const response = await fetch(url);

      if (!response.ok) throw new Error("Failed to fetch file content");

      return await response.text();
    } catch (error: any) {
      console.error("Get text content error:", error);
      throw new Error(error.message || "Failed to get text content");
    }
  },

  // Get user statistics
  async getUserStatistics(userId: string): Promise<{
    totalDocuments: number;
    publicDocuments: number;
    totalDownloads: number;
    storageUsed: number;
  }> {
    try {
      // Get all user documents with their stats
      const { data: documents, error } = await supabase
        .from("documents")
        .select("id, is_public, download_count, file_size")
        .eq("user_id", userId);

      if (error) throw error;

      const stats = {
        totalDocuments: documents?.length || 0,
        publicDocuments: documents?.filter((doc) => doc.is_public).length || 0,
        totalDownloads:
          documents?.reduce((sum, doc) => sum + (doc.download_count || 0), 0) ||
          0,
        storageUsed:
          documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0,
      };

      return stats;
    } catch (error: any) {
      console.error("Get user statistics error:", error);
      // Return zero stats instead of throwing to prevent app crash
      return {
        totalDocuments: 0,
        publicDocuments: 0,
        totalDownloads: 0,
        storageUsed: 0,
      };
    }
  },

  // Get enhanced dashboard statistics
  async getDashboardStatistics(userId: string): Promise<{
    totalDocuments: number;
    sharedFiles: number;
    activeRooms: number;
    storageUsed: number;
    recentFiles: Array<{
      id: string;
      title: string;
      file_type: string;
      file_size: number;
      created_at: string;
    }>;
  }> {
    try {
      // Get user documents
      const { data: documents, error: docsError } = await supabase
        .from("documents")
        .select("id, title, file_type, file_size, created_at")
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (docsError) throw docsError;

      // Get shared files count (documents shared with this user)
      const { count: sharedCount, error: sharedError } = await supabase
        .from("document_shares")
        .select("*", { count: "exact", head: true })
        .eq("shared_with", userId);

      if (sharedError) throw sharedError;

      // Get room documents shared with user
      const { data: roomMemberships, error: roomMemberError } = await supabase
        .from("room_members")
        .select("room_id")
        .eq("user_id", userId);

      if (roomMemberError) throw roomMemberError;

      let roomSharedCount = 0;
      if (roomMemberships && roomMemberships.length > 0) {
        const roomIds = roomMemberships.map((m) => m.room_id);
        const { count: roomDocsCount, error: roomDocsError } = await supabase
          .from("room_documents")
          .select("*", { count: "exact", head: true })
          .in("room_id", roomIds)
          .neq("shared_by", userId); // Don't count documents shared by the user themselves

        if (!roomDocsError) {
          roomSharedCount = roomDocsCount || 0;
        }
      }

      // Get active rooms count (rooms user is a member of)
      // Since room creators are automatically added to room_members table,
      // we only need to count from room_members to avoid double-counting
      const { count: activeRoomsCount, error: roomsError } = await supabase
        .from("room_members")
        .select("*", { count: "exact", head: true })
        .eq("user_id", userId);

      if (roomsError) throw roomsError;

      const stats = {
        totalDocuments: documents?.length || 0,
        sharedFiles: (sharedCount || 0) + roomSharedCount,
        activeRooms: activeRoomsCount || 0,
        storageUsed:
          documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0,
        recentFiles: documents?.slice(0, 5) || [],
      };

      return stats;
    } catch (error: any) {
      console.error("Get dashboard statistics error:", error);
      // Return zero stats instead of throwing to prevent app crash
      return {
        totalDocuments: 0,
        sharedFiles: 0,
        activeRooms: 0,
        storageUsed: 0,
        recentFiles: [],
      };
    }
  },

  // Test function to verify document exists
  async verifyDocumentExists(documentId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("documents")
        .select("id")
        .eq("id", documentId)
        .single();

      if (error && error.code === "PGRST116") {
        // Document not found
        return false;
      }

      return !!data;
    } catch (error) {
      console.error("Error verifying document existence:", error);
      return false;
    }
  },

  // Copy document to another folder (creates a duplicate)
  async copyDocument(
    documentId: string,
    targetFolderId: string | null,
    userId: string,
    newTitle?: string
  ): Promise<DocumentFile> {
    try {
      // Get the source document
      const { data: sourceDoc, error: fetchError } = await supabase
        .from("documents")
        .select("*")
        .eq("id", documentId)
        .eq("user_id", userId)
        .single();

      if (fetchError || !sourceDoc) {
        throw new Error("Document not found or access denied");
      }

      return await this.copyDocumentForUser(
        sourceDoc,
        targetFolderId,
        userId,
        newTitle
      );
    } catch (error: any) {
      console.error("Copy document error:", error);
      throw new Error(error.message || "Failed to copy document");
    }
  },

  // Create shared document record without copying file (to avoid RLS issues)
  async createSharedDocumentRecord(
    sourceDoc: any,
    recipientUserId: string,
    newTitle: string,
    sharedBy?: string
  ): Promise<DocumentFile> {
    try {
      // Create a document record that references the original file
      // The file will be copied later when needed
      const { data: newDoc, error: createError } = await supabase
        .from("documents")
        .insert({
          user_id: recipientUserId,
          folder_id: null, // Goes to root folder
          title: newTitle,
          description: sourceDoc.description,
          file_path: sourceDoc.file_path, // Initially reference the original file
          file_size: sourceDoc.file_size,
          file_type: sourceDoc.file_type,
          mime_type: sourceDoc.mime_type,
          is_public: false,
          is_shared_copy: true,
          original_document_id: sourceDoc.id,
          shared_from_user_id: sharedBy,
          shared_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      return newDoc;
    } catch (error: any) {
      console.error("Create shared document record error:", error);
      throw new Error(
        error.message || "Failed to create shared document record"
      );
    }
  },

  // Copy file for shared document when first accessed (lazy copying)
  async ensureSharedFileExists(
    documentId: string,
    userId: string
  ): Promise<string> {
    try {
      // Get the document record
      const { data: document, error: docError } = await supabase
        .from("documents")
        .select("*")
        .eq("id", documentId)
        .eq("user_id", userId)
        .single();

      if (docError || !document) {
        throw new Error("Document not found");
      }

      // Check if this is a shared document that needs file copying
      const { data: shareRecord } = await supabase
        .from("document_shares")
        .select("original_document_id")
        .eq("shared_document_id", documentId)
        .single();

      if (shareRecord) {
        // This is a shared document, check if file needs to be copied
        const userFilePath = `${userId}/${Date.now()}_${document.title.replace(
          /[^a-zA-Z0-9]/g,
          "_"
        )}.${document.file_type}`;

        // Check if file already exists in user's storage
        const { data: existingFile } = await supabase.storage
          .from("documents")
          .list(userId, { search: document.file_path.split("/").pop() });

        if (!existingFile || existingFile.length === 0) {
          // File doesn't exist in user's storage, copy it
          const { data: fileData, error: downloadError } =
            await supabase.storage
              .from("documents")
              .download(document.file_path);

          if (!downloadError && fileData) {
            const { error: uploadError } = await supabase.storage
              .from("documents")
              .upload(userFilePath, fileData);

            if (!uploadError) {
              // Update document record with new file path
              await supabase
                .from("documents")
                .update({ file_path: userFilePath })
                .eq("id", documentId);

              return userFilePath;
            }
          }
        }
      }

      return document.file_path;
    } catch (error: any) {
      console.error("Ensure shared file exists error:", error);
      // Return original path if copying fails
      const { data: document } = await supabase
        .from("documents")
        .select("file_path")
        .eq("id", documentId)
        .single();

      return document?.file_path || "";
    }
  },

  // Copy document for sharing (internal helper)
  async copyDocumentForUser(
    sourceDoc: any,
    targetFolderId: string | null,
    userId: string,
    newTitle?: string
  ): Promise<DocumentFile> {
    try {
      // Check storage availability before copying
      const hasSpace = await storageService.checkStorageAvailable(
        userId,
        sourceDoc.file_size
      );
      if (!hasSpace) {
        throw new Error("Insufficient storage space to copy this document");
      }

      // Validate target folder if specified
      if (targetFolderId) {
        const { data: targetFolder, error: folderError } = await supabase
          .from("folders")
          .select("id")
          .eq("id", targetFolderId)
          .eq("user_id", userId)
          .single();

        if (folderError || !targetFolder) {
          throw new Error("Target folder not found or access denied");
        }
      }

      // Copy the file in storage
      const originalPath = sourceDoc.file_path;
      const fileExtension = originalPath.split(".").pop();
      const timestamp = Date.now();
      const newFileName = `${timestamp}_copy.${fileExtension}`;
      const newFilePath = `${userId}/${newFileName}`;

      // Copy file in Supabase storage

      try {
        // First try the direct copy method
        const { error: copyError } = await supabase.storage
          .from("documents")
          .copy(originalPath, newFilePath);

        if (copyError) {
          // Direct copy failed, try download/upload method as fallback

          // Fallback: Download and re-upload
          const { data: fileData, error: downloadError } =
            await supabase.storage.from("documents").download(originalPath);

          if (downloadError) {
            throw new Error(
              `Failed to download original file: ${downloadError.message}`
            );
          }

          const { error: uploadError } = await supabase.storage
            .from("documents")
            .upload(newFilePath, fileData, {
              cacheControl: "3600",
              upsert: false,
            });

          if (uploadError) {
            throw new Error(
              `Failed to upload copied file: ${uploadError.message}`
            );
          }
        }
      } catch (error: any) {
        console.error("Storage copy error:", error);
        throw new Error(`Failed to copy file in storage: ${error.message}`);
      }

      // Create new document record
      const documentTitle = newTitle || `${sourceDoc.title} (Copy)`;
      const { data: newDoc, error: createError } = await supabase
        .from("documents")
        .insert({
          user_id: userId,
          folder_id: targetFolderId,
          title: documentTitle,
          description: sourceDoc.description,
          file_path: newFilePath,
          file_size: sourceDoc.file_size,
          file_type: sourceDoc.file_type,
          mime_type: sourceDoc.mime_type,
          is_public: false, // Copies are private by default
        })
        .select()
        .single();

      if (createError) {
        // Clean up the copied file if document creation fails
        await supabase.storage.from("documents").remove([newFilePath]);
        throw createError;
      }

      // Track storage usage for the copied document
      await storageService.trackStorageChange(
        userId,
        "add",
        sourceDoc.file_size,
        `Document copied: ${documentTitle}`
      );

      return newDoc;
    } catch (error: any) {
      console.error("Copy document for user error:", error);
      throw new Error(error.message || "Failed to copy document");
    }
  },

  // Copy multiple documents to a folder
  async copyDocuments(
    documentIds: string[],
    targetFolderId: string | null,
    userId: string
  ): Promise<DocumentFile[]> {
    try {
      const copiedDocuments: DocumentFile[] = [];

      for (const documentId of documentIds) {
        try {
          const copiedDoc = await this.copyDocument(
            documentId,
            targetFolderId,
            userId
          );
          copiedDocuments.push(copiedDoc);
        } catch (error) {
          console.error(`Failed to copy document ${documentId}:`, error);
          // Continue with other documents
        }
      }

      return copiedDocuments;
    } catch (error: any) {
      console.error("Copy documents error:", error);
      throw new Error(error.message || "Failed to copy documents");
    }
  },

  // Get documents that can be moved/copied (excluding shared documents)
  async getMovableDocuments(userId: string): Promise<DocumentFile[]> {
    try {
      const { data: documents, error } = await supabase
        .from("documents")
        .select("*")
        .eq("user_id", userId)
        .order("title");

      if (error) throw error;
      return documents || [];
    } catch (error: any) {
      console.error("Get movable documents error:", error);
      throw new Error(error.message || "Failed to get movable documents");
    }
  },

  // Organize documents into folders (batch move operation)
  async organizeDocuments(
    operations: Array<{
      documentId: string;
      targetFolderId: string | null;
      operation: "move" | "copy";
    }>,
    userId: string
  ): Promise<{
    moved: DocumentFile[];
    copied: DocumentFile[];
    failed: Array<{ documentId: string; error: string }>;
  }> {
    try {
      const moved: DocumentFile[] = [];
      const copied: DocumentFile[] = [];
      const failed: Array<{ documentId: string; error: string }> = [];

      for (const op of operations) {
        try {
          if (op.operation === "move") {
            await this.moveDocumentsToFolder(
              [op.documentId],
              op.targetFolderId,
              userId
            );
            // Get the updated document
            const { data: doc } = await supabase
              .from("documents")
              .select("*")
              .eq("id", op.documentId)
              .single();
            if (doc) moved.push(doc);
          } else if (op.operation === "copy") {
            const copiedDoc = await this.copyDocument(
              op.documentId,
              op.targetFolderId,
              userId
            );
            copied.push(copiedDoc);
          }
        } catch (error: any) {
          failed.push({
            documentId: op.documentId,
            error: error.message || "Unknown error",
          });
        }
      }

      return { moved, copied, failed };
    } catch (error: any) {
      console.error("Organize documents error:", error);
      throw new Error(error.message || "Failed to organize documents");
    }
  },
};
