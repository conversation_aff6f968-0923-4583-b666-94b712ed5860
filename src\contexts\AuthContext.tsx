import React, { createContext, useContext, useEffect, useState } from "react";
import { authService, type AuthUser } from "../lib/auth";
import { profileService } from "../lib/profileService";
import {
  checkUserActiveStatus,
  setupUserStatusListener,
} from "../lib/auth/userStatusCheck";
import toast from "react-hot-toast";

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean; // For route guards (initial auth check)
  actionLoading: boolean; // For form submissions (login, register, etc.)
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: {
    email: string;
    password: string;
    fullName: string;
  }) => Promise<void>;
  signOut: (navigate?: (path: string) => void) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (password: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true); // For route guards
  const [actionLoading, setActionLoading] = useState(false); // For form submissions

  useEffect(() => {
    // Set a timeout to ensure loading doesn't hang forever
    const timeoutId = setTimeout(() => {
      console.warn("Auth initialization timeout, setting loading to false");
      setLoading(false);
    }, 5000); // 5 second timeout

    // Get initial user
    authService
      .getCurrentUser()
      .then((user) => {
        clearTimeout(timeoutId);
        setUser(user);
        setLoading(false);
      })
      .catch((error) => {
        clearTimeout(timeoutId);
        console.error("Error getting initial user:", error);
        setUser(null);
        setLoading(false);
      });

    // Listen for auth changes
    const {
      data: { subscription },
    } = authService.onAuthStateChange(async (user) => {
      // Ensure user has a profile (fallback if database trigger fails)
      if (user?.id && user?.email) {
        try {
          await profileService.ensureUserProfile({
            id: user.id,
            email: user.email,
            user_metadata: {
              full_name: user.profile?.full_name,
              role: user.profile?.role,
              institution: user.profile?.institution,
            },
          } as any);

          // Check if user is active
          const isActive = await checkUserActiveStatus();
          if (!isActive) {
            // User is deactivated, don't set user state
            setUser(null);
            setLoading(false);
            return;
          }

          // Set up real-time listener for user status changes
          setupUserStatusListener(user.id, () => {
            // Handle user deactivation by clearing user state
            // The ProtectedRoute will handle the redirect
            setUser(null);
            toast.error(
              "Your account has been deactivated. Please contact support."
            );
          });
        } catch (error) {
          console.error("Failed to ensure user profile:", error);
          // Don't block user session for profile creation failures
        }
      }

      setUser(user);
      setLoading(false);
    });

    return () => {
      clearTimeout(timeoutId);
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setActionLoading(true);
      await authService.signIn({ email, password });
      toast.success("Welcome back!");
    } catch (error: any) {
      console.error("Sign in error:", error);
      // Keep specific error messages for authentication as they are user-friendly
      toast.error(
        error.message || "Unable to sign in. Please try again later."
      );
      throw error;
    } finally {
      setActionLoading(false);
    }
  };

  const signUp = async (data: {
    email: string;
    password: string;
    fullName: string;
  }) => {
    try {
      setActionLoading(true);
      await authService.signUp(data);
      toast.success(
        "Account created! Please check your email to verify your account."
      );
    } catch (error: any) {
      console.error("Sign up error:", error);
      toast.error("Unable to create account. Please try again later.");
      throw error;
    } finally {
      setActionLoading(false);
    }
  };

  const signOut = async (navigate?: (path: string) => void) => {
    try {
      await authService.signOut();
      // Clear user state immediately
      setUser(null);
      toast.success("Signed out successfully");

      // Navigate to landing page if navigate function is provided
      if (navigate) {
        navigate("/");
      }
    } catch (error: any) {
      console.error("Sign out error:", error);
      toast.error("Unable to sign out. Please try again.");
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setActionLoading(true);
      await authService.resetPassword(email);
      toast.success("Password reset email sent!");
    } catch (error: any) {
      console.error("Password reset error:", error);
      toast.error("Unable to send reset email. Please try again later.");
      throw error;
    } finally {
      setActionLoading(false);
    }
  };

  const updatePassword = async (password: string) => {
    try {
      setActionLoading(true);
      await authService.updatePassword(password);
      toast.success("Password updated successfully!");
    } catch (error: any) {
      console.error("Password update error:", error);
      toast.error("Unable to update password. Please try again later.");
      throw error;
    } finally {
      setActionLoading(false);
    }
  };

  const value = {
    user,
    loading,
    actionLoading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
